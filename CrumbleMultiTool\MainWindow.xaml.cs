using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Threading.Tasks;
using System.Management.Automation;
using System.Collections.ObjectModel;

namespace CrumbleMultiTool;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private readonly StringBuilder _consoleBuffer = new StringBuilder();
    private bool _isInitialized = false;

    public MainWindow()
    {
        InitializeComponent();
        Loaded += MainWindow_Loaded;
    }

    private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        await InitializeConsole();
    }

    private async Task InitializeConsole()
    {
        // Display header ASCII art
        DisplayHeader();

        // Simulate loading
        await Task.Delay(1000);

        // Display main menu
        DisplayMainMenu();

        _isInitialized = true;
        InputTextBox.Focus();
    }

    private void DisplayHeader()
    {
        string headerArt = @"
 ██████╗██████╗ ██╗   ██╗███╗   ███╗██████╗ ██╗     ███████╗
██╔════╝██╔══██╗██║   ██║████╗ ████║██╔══██╗██║     ██╔════╝
██║     ██████╔╝██║   ██║██╔████╔██║██████╔╝██║     █████╗
██║     ██╔══██╗██║   ██║██║╚██╔╝██║██╔══██╗██║     ██╔══╝
╚██████╗██║  ██║╚██████╔╝██║ ╚═╝ ██║██████╔╝███████╗███████╗
 ╚═════╝╚═╝  ╚═╝ ╚═════╝ ╚═╝     ╚═╝╚═════╝ ╚══════╝╚══════╝

            ███╗   ███╗██╗   ██╗██╗  ████████╗██╗
            ████╗ ████║██║   ██║██║  ╚══██╔══╝██║
            ██╔████╔██║██║   ██║██║     ██║   ██║
            ██║╚██╔╝██║██║   ██║██║     ██║   ██║
            ██║ ╚═╝ ██║╚██████╔╝███████╗██║   ██║
            ╚═╝     ╚═╝ ╚═════╝ ╚══════╝╚═╝   ╚═╝

                ████████╗ ██████╗  ██████╗ ██╗
                ╚══██╔══╝██╔═══██╗██╔═══██╗██║
                   ██║   ██║   ██║██║   ██║██║
                   ██║   ██║   ██║██║   ██║██║
                   ██║   ╚██████╔╝╚██████╔╝███████╗
                   ╚═╝    ╚═════╝  ╚═════╝ ╚══════╝
";
        HeaderText.Text = headerArt;
    }

    private void DisplayMainMenu()
    {
        _consoleBuffer.Clear();
        _consoleBuffer.AppendLine("╔══════════════════════════════════════════════════════════════════════════════╗");
        _consoleBuffer.AppendLine("║                              CRUMBLE MULTI TOOL                             ║");
        _consoleBuffer.AppendLine("║                                 Version 1.0                                 ║");
        _consoleBuffer.AppendLine("║                            Created by Steven                                ║");
        _consoleBuffer.AppendLine("╠══════════════════════════════════════════════════════════════════════════════╣");
        _consoleBuffer.AppendLine("║                                                                              ║");
        _consoleBuffer.AppendLine("║  ┌─────────────────────────────────────────────────────────────────────┐   ║");
        _consoleBuffer.AppendLine("║  │                        AVAILABLE TOOLS                             │   ║");
        _consoleBuffer.AppendLine("║  └─────────────────────────────────────────────────────────────────────┘   ║");
        _consoleBuffer.AppendLine("║                                                                              ║");
        _consoleBuffer.AppendLine("║     (1) Process Hacker 2                                                    ║");
        _consoleBuffer.AppendLine("║     (2) Wireshark                                                           ║");
        _consoleBuffer.AppendLine("║     (3) VirtualBox                                                          ║");
        _consoleBuffer.AppendLine("║     (4) Putty                                                               ║");
        _consoleBuffer.AppendLine("║     (5) System Information                                                  ║");
        _consoleBuffer.AppendLine("║     (6) Network Tools                                                       ║");
        _consoleBuffer.AppendLine("║     (7) File Manager                                                        ║");
        _consoleBuffer.AppendLine("║     (8) PowerShell Console                                                  ║");
        _consoleBuffer.AppendLine("║     (9) About                                                               ║");
        _consoleBuffer.AppendLine("║     (0) Exit                                                                ║");
        _consoleBuffer.AppendLine("║                                                                              ║");
        _consoleBuffer.AppendLine("╚══════════════════════════════════════════════════════════════════════════════╝");
        _consoleBuffer.AppendLine("");
        _consoleBuffer.AppendLine("Select Option: ");

        ConsoleOutput.Text = _consoleBuffer.ToString();
    }

    private void InputTextBox_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter)
        {
            ExecuteCommand();
        }
    }

    private void ExecuteButton_Click(object sender, RoutedEventArgs e)
    {
        ExecuteCommand();
    }

    private async void ExecuteCommand()
    {
        if (!_isInitialized) return;

        string input = InputTextBox.Text.Trim();
        InputTextBox.Clear();

        if (string.IsNullOrEmpty(input)) return;

        // Add user input to console
        _consoleBuffer.AppendLine($"> {input}");
        _consoleBuffer.AppendLine("");

        // Process the command
        await ProcessMenuSelection(input);

        // Update console display
        ConsoleOutput.Text = _consoleBuffer.ToString();

        // Auto-scroll to bottom
        var scrollViewer = FindVisualChild<ScrollViewer>(ConsoleOutput.Parent as DependencyObject);
        scrollViewer?.ScrollToEnd();
    }

    private async Task ProcessMenuSelection(string input)
    {
        switch (input.ToLower())
        {
            case "1":
                await LaunchTool("Process Hacker 2", "ProcessHacker.exe");
                break;
            case "2":
                await LaunchTool("Wireshark", "wireshark.exe");
                break;
            case "3":
                await LaunchTool("VirtualBox", "VirtualBox.exe");
                break;
            case "4":
                await LaunchTool("Putty", "putty.exe");
                break;
            case "5":
                await ShowSystemInformation();
                break;
            case "6":
                await ShowNetworkTools();
                break;
            case "7":
                await ShowFileManager();
                break;
            case "8":
                await ShowPowerShellConsole();
                break;
            case "9":
                ShowAbout();
                break;
            case "0":
            case "exit":
                Application.Current.Shutdown();
                break;
            case "clear":
                DisplayMainMenu();
                return;
            case "help":
                ShowHelp();
                break;
            default:
                _consoleBuffer.AppendLine($"[ERROR] Invalid option: {input}");
                _consoleBuffer.AppendLine("Type 'help' for available commands or select a number from the menu.");
                break;
        }

        _consoleBuffer.AppendLine("");
        _consoleBuffer.AppendLine("Press any key to continue or type 'clear' to return to main menu...");
    }

    private async Task LaunchTool(string toolName, string executable)
    {
        _consoleBuffer.AppendLine($"[INFO] Launching {toolName}...");

        try
        {
            // Simulate tool launch
            await Task.Delay(500);
            _consoleBuffer.AppendLine($"[SUCCESS] {toolName} launched successfully!");
            _consoleBuffer.AppendLine($"[INFO] Executable: {executable}");

            // Here you would add actual process launching code
            // System.Diagnostics.Process.Start(executable);
        }
        catch (Exception ex)
        {
            _consoleBuffer.AppendLine($"[ERROR] Failed to launch {toolName}: {ex.Message}");
        }
    }

    private async Task ShowSystemInformation()
    {
        _consoleBuffer.AppendLine("[INFO] Gathering system information...");
        await Task.Delay(1000);

        _consoleBuffer.AppendLine("╔══════════════════════════════════════════════════════════════════════════════╗");
        _consoleBuffer.AppendLine("║                              SYSTEM INFORMATION                             ║");
        _consoleBuffer.AppendLine("╚══════════════════════════════════════════════════════════════════════════════╝");
        _consoleBuffer.AppendLine($"OS Version: {Environment.OSVersion}");
        _consoleBuffer.AppendLine($"Machine Name: {Environment.MachineName}");
        _consoleBuffer.AppendLine($"User Name: {Environment.UserName}");
        _consoleBuffer.AppendLine($"Processor Count: {Environment.ProcessorCount}");
        _consoleBuffer.AppendLine($"Working Set: {Environment.WorkingSet / 1024 / 1024} MB");
        _consoleBuffer.AppendLine($"CLR Version: {Environment.Version}");
    }

    private async Task ShowNetworkTools()
    {
        _consoleBuffer.AppendLine("[INFO] Loading network tools...");
        await Task.Delay(500);

        _consoleBuffer.AppendLine("╔══════════════════════════════════════════════════════════════════════════════╗");
        _consoleBuffer.AppendLine("║                               NETWORK TOOLS                                 ║");
        _consoleBuffer.AppendLine("╚══════════════════════════════════════════════════════════════════════════════╝");
        _consoleBuffer.AppendLine("Available Network Commands:");
        _consoleBuffer.AppendLine("• ping <hostname>     - Ping a host");
        _consoleBuffer.AppendLine("• nslookup <domain>   - DNS lookup");
        _consoleBuffer.AppendLine("• netstat             - Show network connections");
        _consoleBuffer.AppendLine("• ipconfig            - Show IP configuration");
        _consoleBuffer.AppendLine("");
        _consoleBuffer.AppendLine("[INFO] Network tools ready for use.");
    }

    private async Task ShowFileManager()
    {
        _consoleBuffer.AppendLine("[INFO] Loading file manager...");
        await Task.Delay(500);

        _consoleBuffer.AppendLine("╔══════════════════════════════════════════════════════════════════════════════╗");
        _consoleBuffer.AppendLine("║                               FILE MANAGER                                  ║");
        _consoleBuffer.AppendLine("╚══════════════════════════════════════════════════════════════════════════════╝");
        _consoleBuffer.AppendLine($"Current Directory: {Environment.CurrentDirectory}");
        _consoleBuffer.AppendLine("");
        _consoleBuffer.AppendLine("Available File Commands:");
        _consoleBuffer.AppendLine("• dir                 - List directory contents");
        _consoleBuffer.AppendLine("• cd <path>           - Change directory");
        _consoleBuffer.AppendLine("• copy <src> <dest>   - Copy file");
        _consoleBuffer.AppendLine("• del <file>          - Delete file");
        _consoleBuffer.AppendLine("");
        _consoleBuffer.AppendLine("[INFO] File manager ready for use.");
    }

    private async Task ShowPowerShellConsole()
    {
        _consoleBuffer.AppendLine("[INFO] Initializing PowerShell console...");
        await Task.Delay(1000);

        _consoleBuffer.AppendLine("╔══════════════════════════════════════════════════════════════════════════════╗");
        _consoleBuffer.AppendLine("║                             POWERSHELL CONSOLE                              ║");
        _consoleBuffer.AppendLine("╚══════════════════════════════════════════════════════════════════════════════╝");
        _consoleBuffer.AppendLine("PowerShell Console Activated!");
        _consoleBuffer.AppendLine("");
        _consoleBuffer.AppendLine("Example Commands:");
        _consoleBuffer.AppendLine("• Get-Process         - List running processes");
        _consoleBuffer.AppendLine("• Get-Service         - List system services");
        _consoleBuffer.AppendLine("• Get-ComputerInfo    - Get computer information");
        _consoleBuffer.AppendLine("• Get-EventLog        - View event logs");
        _consoleBuffer.AppendLine("");
        _consoleBuffer.AppendLine("[INFO] PowerShell console ready for commands.");
    }

    private void ShowAbout()
    {
        _consoleBuffer.AppendLine("╔══════════════════════════════════════════════════════════════════════════════╗");
        _consoleBuffer.AppendLine("║                                   ABOUT                                     ║");
        _consoleBuffer.AppendLine("╚══════════════════════════════════════════════════════════════════════════════╝");
        _consoleBuffer.AppendLine("Crumble Multi Tool v1.0");
        _consoleBuffer.AppendLine("Created by: Steven");
        _consoleBuffer.AppendLine("Built with: WPF & .NET 9.0");
        _consoleBuffer.AppendLine("");
        _consoleBuffer.AppendLine("A retro-style multi-tool interface for launching");
        _consoleBuffer.AppendLine("various system utilities and PowerShell commands.");
        _consoleBuffer.AppendLine("");
        _consoleBuffer.AppendLine("© 2024 Crumble Multi Tool. All rights reserved.");
    }

    private void ShowHelp()
    {
        _consoleBuffer.AppendLine("╔══════════════════════════════════════════════════════════════════════════════╗");
        _consoleBuffer.AppendLine("║                                   HELP                                      ║");
        _consoleBuffer.AppendLine("╚══════════════════════════════════════════════════════════════════════════════╝");
        _consoleBuffer.AppendLine("Available Commands:");
        _consoleBuffer.AppendLine("• 1-9     - Select menu option");
        _consoleBuffer.AppendLine("• 0       - Exit application");
        _consoleBuffer.AppendLine("• clear   - Return to main menu");
        _consoleBuffer.AppendLine("• help    - Show this help message");
        _consoleBuffer.AppendLine("• exit    - Exit application");
        _consoleBuffer.AppendLine("");
        _consoleBuffer.AppendLine("Navigation:");
        _consoleBuffer.AppendLine("• Type a number and press Enter");
        _consoleBuffer.AppendLine("• Use the Execute button or Enter key");
        _consoleBuffer.AppendLine("• Scroll to view console history");
    }

    // Helper method to find visual children
    private static T FindVisualChild<T>(DependencyObject obj) where T : DependencyObject
    {
        for (int i = 0; i < VisualTreeHelper.GetChildrenCount(obj); i++)
        {
            DependencyObject child = VisualTreeHelper.GetChild(obj, i);
            if (child != null && child is T)
                return (T)child;
            else
            {
                T childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
        }
        return null;
    }
}