<Window x:Class="CrumbleMultiTool.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CrumbleMultiTool"
        mc:Ignorable="d"
        Title="Crumble Multi Tool" Height="600" Width="900"
        Background="Black"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header Section with ASCII Art -->
        <Border Grid.Row="0" Background="#1a1a1a" BorderBrush="#333" BorderThickness="0,0,0,2" Padding="20,10">
            <TextBlock x:Name="HeaderText"
                       FontFamily="Consolas"
                       FontSize="14"
                       Foreground="#00ff00"
                       TextAlignment="Center"
                       Text="Loading..." />
        </Border>

        <!-- Main Console Area -->
        <ScrollViewer Grid.Row="1"
                      Background="Black"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Auto"
                      Padding="20">
            <TextBlock x:Name="ConsoleOutput"
                       FontFamily="Consolas"
                       FontSize="12"
                       Foreground="#00ff00"
                       Background="Black"
                       TextWrapping="NoWrap"
                       Text="Initializing..." />
        </ScrollViewer>

        <!-- Input Section -->
        <Border Grid.Row="2" Background="#1a1a1a" BorderBrush="#333" BorderThickness="0,2,0,0" Padding="20,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                           Text="Select Option: "
                           FontFamily="Consolas"
                           FontSize="12"
                           Foreground="#00ff00"
                           VerticalAlignment="Center"
                           Margin="0,0,10,0"/>

                <TextBox x:Name="InputTextBox"
                         Grid.Column="1"
                         FontFamily="Consolas"
                         FontSize="12"
                         Background="Black"
                         Foreground="#00ff00"
                         BorderBrush="#333"
                         BorderThickness="1"
                         Padding="5"
                         KeyDown="InputTextBox_KeyDown"/>

                <Button x:Name="ExecuteButton"
                        Grid.Column="2"
                        Content="Execute"
                        FontFamily="Consolas"
                        FontSize="12"
                        Background="#333"
                        Foreground="#00ff00"
                        BorderBrush="#555"
                        BorderThickness="1"
                        Padding="10,5"
                        Margin="10,0,0,0"
                        Click="ExecuteButton_Click"/>
            </Grid>
        </Border>
    </Grid>
</Window>
